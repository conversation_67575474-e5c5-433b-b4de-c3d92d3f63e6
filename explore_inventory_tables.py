import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Explore the structure of key inventory tables ---
tables_to_explore = ['mard', 'mbew', 'mchb']

for table_name in tables_to_explore:
    print(f"\n🔍 Exploring table: {table_name}")
    
    # Get table schema
    schema_query = f"DESCRIBE brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.{table_name}"
    
    try:
        with conn.cursor() as cur:
            cur.execute(schema_query)
            schema_df = cur.fetchall_arrow().to_pandas()
        
        print(f"Schema for {table_name}:")
        for _, row in schema_df.iterrows():
            print(f"  {row['col_name']}: {row['data_type']}")
        
        # Get sample data for materials starting with 7
        sample_query = f"""
        SELECT * FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.{table_name}
        WHERE matnr LIKE '000000000007%' AND werks LIKE 'DE%'
        LIMIT 5
        """
        
        with conn.cursor() as cur:
            cur.execute(sample_query)
            sample_df = cur.fetchall_arrow().to_pandas()
        
        print(f"\nSample data from {table_name} (first 5 rows):")
        if not sample_df.empty:
            print(sample_df.to_string())
        else:
            print("No sample data found")
            
    except Exception as e:
        print(f"Error exploring {table_name}: {e}")

conn.close()
print("\n✅ Exploration complete.")
