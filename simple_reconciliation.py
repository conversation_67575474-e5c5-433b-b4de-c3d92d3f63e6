import pandas as pd
import numpy as np

print("🔍 SIMPLE RECONCILIATION ANALYSIS")
print("=" * 60)

# Load SOT files and our results
sot_36m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv')
sot_9m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv')
our_36m = pd.read_csv('final_raw_numbers_36_month.csv')
our_9m = pd.read_csv('final_raw_numbers_9_month.csv')

print(f"SOT 36m records: {len(sot_36m)}")
print(f"SOT 9m records: {len(sot_9m)}")
print(f"Our 36m records: {len(our_36m)}")
print(f"Our 9m records: {len(our_9m)}")

# Focus on the specific discrepancies we identified
problem_cases = [
    ('7546979', 'DE30', '36m'),
    ('7546979', 'DE30', '9m'),
    ('7546980', 'DE02', '36m'),
    ('7546980', 'DE02', '9m'),
]

print(f"\n📊 DETAILED COMPARISON FOR PROBLEM CASES")
print("=" * 60)

for material, plant, period in problem_cases:
    print(f"\n🎯 Material {material}, Plant {plant}, Period {period}")
    print("-" * 40)
    
    if period == '36m':
        sot_data = sot_36m[(sot_36m['Material'] == int(material)) & (sot_36m['Plnt'] == plant)]
        our_data = our_36m[(our_36m['Material'] == material) & (our_36m['Plant'] == plant)]
        cols = ['Opening Stock (36m)', 'Total Receipts (36m)', 'Total Issues (36m)', 'Closing Stock (36m)']
    else:
        sot_data = sot_9m[(sot_9m['Material'] == int(material)) & (sot_9m['Plnt'] == plant)]
        our_data = our_9m[(our_9m['Material'] == material) & (our_9m['Plant'] == plant)]
        cols = ['Opening Stock (9m)', 'Total Receipts (9m)', 'Total Issues (9m)', 'Closing Stock (9m)']
    
    if not sot_data.empty and not our_data.empty:
        sot_row = sot_data.iloc[0]
        our_row = our_data.iloc[0]
        
        opening_diff = our_row[cols[0]] - sot_row['Opening St']
        receipts_diff = our_row[cols[1]] - sot_row['Total Rece']
        issues_diff = our_row[cols[2]] - sot_row['Total Issu']
        closing_diff = our_row[cols[3]] - sot_row['Closing St']
        
        print(f"Opening Stock: SOT={sot_row['Opening St']}, Ours={our_row[cols[0]]:.1f}, Diff={opening_diff:.1f}")
        print(f"Total Receipts: SOT={sot_row['Total Rece']}, Ours={our_row[cols[1]]:.1f}, Diff={receipts_diff:.1f}")
        print(f"Total Issues: SOT={sot_row['Total Issu']}, Ours={our_row[cols[2]]:.1f}, Diff={issues_diff:.1f}")
        print(f"Closing Stock: SOT={sot_row['Closing St']}, Ours={our_row[cols[3]]:.1f}, Diff={closing_diff:.1f}")
        
        # Check if the differences are consistent
        net_movement_diff = receipts_diff + issues_diff
        print(f"Net Movement Diff: {net_movement_diff:.1f}")
        print(f"Opening-Closing Consistency: {abs(opening_diff - closing_diff):.1f} (should be 0 if consistent)")

# Now let's do a broader analysis to see patterns
print(f"\n📈 BROADER ANALYSIS - Looking for Patterns")
print("=" * 60)

# Merge SOT and our data for comparison
sot_36m_clean = sot_36m[['Material', 'Plnt', 'Opening St', 'Total Rece', 'Total Issu', 'Closing St']].copy()
sot_36m_clean.columns = ['Material', 'Plant', 'SOT_Opening_36m', 'SOT_Receipts_36m', 'SOT_Issues_36m', 'SOT_Closing_36m']
sot_36m_clean['Material'] = sot_36m_clean['Material'].astype(str)  # Convert to string

our_36m_clean = our_36m[['Material', 'Plant', 'Opening Stock (36m)', 'Total Receipts (36m)', 'Total Issues (36m)', 'Closing Stock (36m)']].copy()
our_36m_clean.columns = ['Material', 'Plant', 'Our_Opening_36m', 'Our_Receipts_36m', 'Our_Issues_36m', 'Our_Closing_36m']
our_36m_clean['Material'] = our_36m_clean['Material'].astype(str)  # Convert to string to match SOT

print(f"SOT Material types: {sot_36m_clean['Material'].dtype}")
print(f"Our Material types: {our_36m_clean['Material'].dtype}")
print(f"Sample SOT materials: {sot_36m_clean['Material'].head(3).tolist()}")
print(f"Sample Our materials: {our_36m_clean['Material'].head(3).tolist()}")

# Merge on Material and Plant
comparison_36m = pd.merge(sot_36m_clean, our_36m_clean, on=['Material', 'Plant'], how='inner')

if not comparison_36m.empty:
    # Calculate differences
    comparison_36m['Opening_Diff'] = comparison_36m['Our_Opening_36m'] - comparison_36m['SOT_Opening_36m']
    comparison_36m['Receipts_Diff'] = comparison_36m['Our_Receipts_36m'] - comparison_36m['SOT_Receipts_36m']
    comparison_36m['Issues_Diff'] = comparison_36m['Our_Issues_36m'] - comparison_36m['SOT_Issues_36m']
    comparison_36m['Closing_Diff'] = comparison_36m['Our_Closing_36m'] - comparison_36m['SOT_Closing_36m']
    
    print(f"36-Month Period - Records matched: {len(comparison_36m)}")
    print(f"Perfect Opening Stock matches: {sum(abs(comparison_36m['Opening_Diff']) < 0.01)}")
    print(f"Perfect Receipts matches: {sum(abs(comparison_36m['Receipts_Diff']) < 0.01)}")
    print(f"Perfect Issues matches: {sum(abs(comparison_36m['Issues_Diff']) < 0.01)}")
    print(f"Perfect Closing Stock matches: {sum(abs(comparison_36m['Closing_Diff']) < 0.01)}")
    
    # Show largest discrepancies
    print(f"\nLargest Opening Stock discrepancies (36m):")
    large_opening_diffs = comparison_36m[abs(comparison_36m['Opening_Diff']) > 10].nlargest(5, 'Opening_Diff')
    for _, row in large_opening_diffs.iterrows():
        print(f"  Material {row['Material']}, Plant {row['Plant']}: Diff={row['Opening_Diff']:.1f}")
    
    print(f"\nLargest Receipts discrepancies (36m):")
    large_receipts_diffs = comparison_36m[abs(comparison_36m['Receipts_Diff']) > 10].nlargest(5, 'Receipts_Diff')
    for _, row in large_receipts_diffs.iterrows():
        print(f"  Material {row['Material']}, Plant {row['Plant']}: Diff={row['Receipts_Diff']:.1f}")

# Check if there are materials in SOT but not in our results
sot_materials = set(sot_36m_clean['Material'] + '_' + sot_36m_clean['Plant'])
our_materials = set(our_36m_clean['Material'] + '_' + our_36m_clean['Plant'])

missing_in_ours = sot_materials - our_materials
extra_in_ours = our_materials - sot_materials

print(f"\nMaterial-Plant combinations in SOT but missing in our results: {len(missing_in_ours)}")
if missing_in_ours and len(missing_in_ours) <= 10:
    for item in list(missing_in_ours)[:10]:
        material, plant = item.split('_')
        print(f"  Material {material}, Plant {plant}")

print(f"\nMaterial-Plant combinations in our results but not in SOT: {len(extra_in_ours)}")
if extra_in_ours and len(extra_in_ours) <= 10:
    for item in list(extra_in_ours)[:10]:
        material, plant = item.split('_')
        print(f"  Material {material}, Plant {plant}")

print(f"\n" + "=" * 60)
print("🎯 RECONCILIATION SUMMARY")
print("   The analysis above shows the specific differences.")
print("   Next steps: Investigate boundary date handling,")
print("   valuation types, or special stock indicators.")
print("=" * 60)
