import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Debug query to find the exact format of our target materials ---
debug_query = """
SELECT 
  matnr AS Material,
  bwkey AS Plant,
  lbkum AS Current_Stock
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
WHERE (matnr LIKE '%7546978%' OR matnr LIKE '%7546979%' OR matnr LIKE '%7546980%')
  AND bwkey LIKE 'DE%'
ORDER BY matnr, bwkey
"""

print("🔍 Searching for materials 7546978, 7546979, 7546980 in MBEW table...")
try:
    with conn.cursor() as cur:
        cur.execute(debug_query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished.")
    
    if not df.empty:
        print(f"Found {len(df)} records:")
        print(df.to_string())
    else:
        print("❌ No records found for these materials in MBEW table")
        
        # Let's check if they exist in MSEG table instead
        print("\n🔍 Checking MSEG table for these materials...")
        mseg_query = """
        SELECT DISTINCT
          matnr AS Material,
          werks AS Plant
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE (matnr LIKE '%7546978%' OR matnr LIKE '%7546979%' OR matnr LIKE '%7546980%')
          AND werks LIKE 'DE%'
        ORDER BY matnr, werks
        LIMIT 20
        """
        
        with conn.cursor() as cur:
            cur.execute(mseg_query)
            mseg_df = cur.fetchall_arrow().to_pandas()
        
        if not mseg_df.empty:
            print(f"Found {len(mseg_df)} material-plant combinations in MSEG:")
            print(mseg_df.to_string())
        else:
            print("❌ No records found in MSEG either")
        
except Exception as e:
    print(f"An error occurred during query execution: {e}")
finally:
    conn.close()
