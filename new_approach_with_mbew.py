import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- New SQL Query using MBEW table for Opening Stock ---
# This approach:
# 1. Uses MBEW table to get current stock levels (lbkum field)
# 2. Calculates movements from MSEG/MKPF for the specific periods
# 3. Derives opening stock by subtracting period movements from current stock
query = """
WITH CurrentStock AS (
  -- Get current stock levels from MBEW table
  SELECT 
    mbew.matnr AS Material,
    mbew.bwkey AS Plant,
    mbew.lbkum AS Current_Stock
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
  WHERE mbew.matnr LIKE '000000000007%' 
    AND mbew.bwkey LIKE 'DE%'
    AND mbew.lbkum != 0  -- Only include materials with current stock
),

PeriodMovements AS (
  -- Get all movements for the relevant periods
  SELECT
    mseg.matnr AS Material,
    mseg.werks AS Plant,
    
    -- 36-month period movements (2022-09-30 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' 
             THEN mseg.menge ELSE 0 END) AS Receipts_36m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' 
             THEN -mseg.menge ELSE 0 END) AS Issues_36m,
    
    -- 9-month period movements (2024-12-01 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' 
             THEN mseg.menge ELSE 0 END) AS Receipts_9m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' 
             THEN -mseg.menge ELSE 0 END) AS Issues_9m
             
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
  JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
    ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
  WHERE mseg.matnr LIKE '000000000007%'
    AND mseg.werks LIKE 'DE%'
    AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
  GROUP BY mseg.matnr, mseg.werks
)

SELECT 
  cs.Material,
  cs.Plant,
  cs.Current_Stock,
  
  -- 36-month period calculations
  COALESCE(pm.Receipts_36m, 0) AS `Total Receipts (36m)`,
  COALESCE(pm.Issues_36m, 0) AS `Total Issues (36m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_36m, 0) - COALESCE(pm.Issues_36m, 0)) AS `Opening Stock (36m)`,
  
  -- 9-month period calculations  
  COALESCE(pm.Receipts_9m, 0) AS `Total Receipts (9m)`,
  COALESCE(pm.Issues_9m, 0) AS `Total Issues (9m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_9m, 0) - COALESCE(pm.Issues_9m, 0)) AS `Opening Stock (9m)`

FROM CurrentStock cs
LEFT JOIN PeriodMovements pm 
  ON cs.Material = pm.Material AND cs.Plant = pm.Plant
ORDER BY cs.Material, cs.Plant
"""

# --- Execute Query and Export Data ---
print("Executing new approach query on Databricks... This may take a moment.")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished. Processing and exporting data...")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

# --- Check if data was returned ---
if df.empty:
    print("\n⚠️ Warning: The query ran successfully but returned no data.")
    sys.exit(0)

print(f"Retrieved {len(df)} material-plant combinations")

# --- Create DataFrames and calculate Closing Stock ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)"]].copy()
df_36m["Closing Stock (36m)"] = df_36m["Opening Stock (36m)"] + df_36m["Total Receipts (36m)"] + df_36m["Total Issues (36m)"]

df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)"]].copy()
df_9m["Closing Stock (9m)"] = df_9m["Opening Stock (9m)"] + df_9m["Total Receipts (9m)"] + df_9m["Total Issues (9m)"]

# --- Convert material numbers to short format (remove leading zeros) ---
df_36m["Material"] = df_36m["Material"].str.lstrip('0')
df_9m["Material"] = df_9m["Material"].str.lstrip('0')

# --- Export DataFrames to CSV files ---
output_file_36m = 'new_approach_36_month.csv'
output_file_9m = 'new_approach_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n✅ Success! The new approach data has been exported to:")
print(f"   - {output_file_36m}")
print(f"   - {output_file_9m}")

# --- Show sample of results ---
print(f"\nSample of 36-month results:")
print(df_36m.head(10).to_string())
print(f"\nSample of 9-month results:")
print(df_9m.head(10).to_string())
