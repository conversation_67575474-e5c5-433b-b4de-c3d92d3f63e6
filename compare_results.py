import pandas as pd
import numpy as np

# Load the SOT files
sot_36m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv')
sot_9m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv')

# Load our MBEW-based results
mbew_36m = pd.read_csv('mbew_solution_36_month.csv')
mbew_9m = pd.read_csv('mbew_solution_9_month.csv')

print("🔍 Comparing MBEW-based results with SOT files...")

# Focus on the specific materials we've been tracking
test_materials = ['7546978', '7546979', '7546980']

# Debug: Check what materials we have in MBEW data
print(f"Sample MBEW materials: {mbew_36m['Material'].head(10).tolist()}")
print(f"Looking for materials: {test_materials}")

# Check if our test materials exist in MBEW data
for material in test_materials:
    exists_36 = material in mbew_36m['Material'].values
    exists_9 = material in mbew_9m['Material'].values
    print(f"Material {material}: 36m={exists_36}, 9m={exists_9}")

print("\n=== 36-Month Period Comparison ===")
for material in test_materials:
    print(f"\nMaterial {material}:")
    
    # SOT data
    sot_data = sot_36m[sot_36m['Material'] == int(material)]
    print("SOT (Source of Truth):")
    for _, row in sot_data.iterrows():
        print(f"  Plant {row['Plnt']}: Opening={row['Opening St']}, Receipts={row['Total Rece']}, Issues={row['Total Issu']}, Closing={row['Closing St']}")
    
    # Our MBEW data
    mbew_data = mbew_36m[mbew_36m['Material'] == material]
    print("MBEW Solution:")
    if mbew_data.empty:
        print(f"  No data found for material {material}")
    else:
        for _, row in mbew_data.iterrows():
            print(f"  Plant {row['Plant']}: Opening={row['Opening Stock (36m)']:.1f}, Receipts={row['Total Receipts (36m)']:.1f}, Issues={row['Total Issues (36m)']:.1f}, Closing={row['Closing Stock (36m)']:.1f}")

print("\n=== 9-Month Period Comparison ===")
for material in test_materials:
    print(f"\nMaterial {material}:")
    
    # SOT data
    sot_data = sot_9m[sot_9m['Material'] == int(material)]
    print("SOT (Source of Truth):")
    for _, row in sot_data.iterrows():
        print(f"  Plant {row['Plnt']}: Opening={row['Opening St']}, Receipts={row['Total Rece']}, Issues={row['Total Issu']}, Closing={row['Closing St']}")
    
    # Our MBEW data
    mbew_data = mbew_9m[mbew_9m['Material'] == material]
    print("MBEW Solution:")
    if mbew_data.empty:
        print(f"  No data found for material {material}")
    else:
        for _, row in mbew_data.iterrows():
            print(f"  Plant {row['Plant']}: Opening={row['Opening Stock (9m)']:.1f}, Receipts={row['Total Receipts (9m)']:.1f}, Issues={row['Total Issues (9m)']:.1f}, Closing={row['Closing Stock (9m)']:.1f}")

# Check if we have the right number of records
print(f"\n=== Record Count Comparison ===")
print(f"SOT 36-month records: {len(sot_36m)}")
print(f"MBEW 36-month records: {len(mbew_36m)}")
print(f"SOT 9-month records: {len(sot_9m)}")
print(f"MBEW 9-month records: {len(mbew_9m)}")

# Check for missing materials in our solution
sot_materials_36 = set(sot_36m['Material'].astype(str))
mbew_materials_36 = set(mbew_36m['Material'])
missing_in_mbew = sot_materials_36 - mbew_materials_36
extra_in_mbew = mbew_materials_36 - sot_materials_36

print(f"\nMaterials in SOT but missing in MBEW solution: {len(missing_in_mbew)}")
if missing_in_mbew and len(missing_in_mbew) <= 10:
    print(f"  {list(missing_in_mbew)}")

print(f"Materials in MBEW solution but not in SOT: {len(extra_in_mbew)}")
if extra_in_mbew and len(extra_in_mbew) <= 10:
    print(f"  {list(extra_in_mbew)}")
