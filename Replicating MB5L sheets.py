import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    print("Please ensure you have a .env file with DATABRICKS_HOST, DATABRICKS_HTTP_PATH, and DATABRICKS_TOKEN.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)


# --- Final SQL Query with Corrected Logic ---
# This version includes:
# 1. The precise LIKE clause for 18-character material numbers.
# 2. Filtering for German plants ('DE%').
# 3. The "snapshot date" logic for calculating Opening Stock.
query = """
SELECT
  mseg.matnr AS `Material`,
  mseg.werks AS `Plant`,

  -- Calculations for 36-month period (Glass)
  -- Snapshot Date: 2018-10-08 | Period Start: 2022-09-30
  SUM(CASE WHEN mkpf.budat BETWEEN '2018-10-08' AND '2022-09-29' THEN
    CASE WHEN mseg.shkzg = 'S' THEN mseg.menge WHEN mseg.shkzg = 'H' THEN -mseg.menge ELSE 0 END
  ELSE 0 END) AS `Opening Stock (36m)`,
  SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' THEN mseg.menge ELSE 0 END) AS `Total Receipts (36m)`,
  SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' THEN -mseg.menge ELSE 0 END) AS `Total Issues (36m)`,

  -- Calculations for 9-month period (Others)
  -- Snapshot Date: 2018-10-08 | Period Start: 2024-12-01
  SUM(CASE WHEN mkpf.budat BETWEEN '2018-10-08' AND '2024-11-30' THEN
    CASE WHEN mseg.shkzg = 'S' THEN mseg.menge WHEN mseg.shkzg = 'H' THEN -mseg.menge ELSE 0 END
  ELSE 0 END) AS `Opening Stock (9m)`,
  SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' THEN mseg.menge ELSE 0 END) AS `Total Receipts (9m)`,
  SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' THEN -mseg.menge ELSE 0 END) AS `Total Issues (9m)`

FROM
  brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
JOIN
  brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
ON
  mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
WHERE
  mseg.matnr LIKE '000000000007%'
  AND mseg.werks LIKE 'DE%'
  AND mkpf.budat <= '2025-08-31'
GROUP BY
  mseg.matnr, mseg.werks
"""

# --- Execute Query and Export Data ---
print("Executing final query on Databricks... This may take a moment.")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished. Processing and exporting data...")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

# --- Check if data was returned ---
if df.empty:
    print("\n⚠️ Warning: The query ran successfully but returned no data.")
    sys.exit(0)

# --- Create DataFrames and calculate Closing Stock ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)"]].copy()
df_36m["Closing Stock (36m)"] = df_36m["Opening Stock (36m)"] + df_36m["Total Receipts (36m)"] + df_36m["Total Issues (36m)"]

df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)"]].copy()
df_9m["Closing Stock (9m)"] = df_9m["Opening Stock (9m)"] + df_9m["Total Receipts (9m)"] + df_9m["Total Issues (9m)"]

# --- Export DataFrames to CSV files ---
output_file_36m = 'final_raw_numbers_36_month.csv'
output_file_9m = 'final_raw_numbers_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n✅ Success! The final data has been exported to:")
print(f"   - {output_file_36m}")
print(f"   - {output_file_9m}")