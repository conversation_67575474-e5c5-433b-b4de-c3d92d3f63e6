import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    print("Please ensure you have a .env file with DATABRICKS_HOST, DATABRICKS_HTTP_PATH, and DATABRICKS_TOKEN.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- First, let's explore the available tables to find inventory snapshot tables ---
print("🔍 Exploring available tables in the schema...")
explore_query = """
SHOW TABLES IN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe
"""

try:
    with conn.cursor() as cur:
        cur.execute(explore_query)
        tables_df = cur.fetchall_arrow().to_pandas()
    print(f"Found {len(tables_df)} tables in the schema.")

    # Look for inventory-related tables
    inventory_tables = tables_df[tables_df['tableName'].str.contains('mb|mard|mchb|stock|inventory', case=False, na=False)]
    print(f"\nPotential inventory tables found:")
    for _, row in inventory_tables.iterrows():
        print(f"  - {row['tableName']}")

except Exception as e:
    print(f"Error exploring tables: {e}")
    # Continue with the main query anyway


# --- NEW APPROACH: Use MBEW table for current stock and calculate backwards ---
# This approach:
# 1. Gets current stock levels from MBEW table (lbkum field)
# 2. Calculates period movements from MSEG/MKPF
# 3. Derives opening stock by working backwards: Opening = Current - Net Movements
query = """
WITH CurrentStock AS (
  SELECT
    matnr AS Material,
    bwkey AS Plant,
    lbkum AS Current_Stock
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
  WHERE matnr LIKE '000000000007%'
    AND bwkey LIKE 'DE%'
),

PeriodMovements AS (
  SELECT
    mseg.matnr AS Material,
    mseg.werks AS Plant,

    -- 36-month period movements (2022-09-30 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S'
             THEN mseg.menge ELSE 0 END) AS Receipts_36m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H'
             THEN -mseg.menge ELSE 0 END) AS Issues_36m,

    -- 9-month period movements (2024-12-01 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S'
             THEN mseg.menge ELSE 0 END) AS Receipts_9m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H'
             THEN -mseg.menge ELSE 0 END) AS Issues_9m

  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
  JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
    ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
  WHERE mseg.matnr LIKE '000000000007%'
    AND mseg.werks LIKE 'DE%'
    AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
  GROUP BY mseg.matnr, mseg.werks
)

SELECT
  cs.Material,
  cs.Plant,
  cs.Current_Stock,

  -- 36-month period calculations
  COALESCE(pm.Receipts_36m, 0) AS `Total Receipts (36m)`,
  COALESCE(pm.Issues_36m, 0) AS `Total Issues (36m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_36m, 0) - COALESCE(pm.Issues_36m, 0)) AS `Opening Stock (36m)`,

  -- 9-month period calculations
  COALESCE(pm.Receipts_9m, 0) AS `Total Receipts (9m)`,
  COALESCE(pm.Issues_9m, 0) AS `Total Issues (9m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_9m, 0) - COALESCE(pm.Issues_9m, 0)) AS `Opening Stock (9m)`

FROM CurrentStock cs
LEFT JOIN PeriodMovements pm
  ON cs.Material = pm.Material AND cs.Plant = pm.Plant
ORDER BY cs.Material, cs.Plant
"""

# --- Execute Query and Export Data ---
print("Executing final query on Databricks... This may take a moment.")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished. Processing and exporting data...")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

# --- Check if data was returned ---
if df.empty:
    print("\n⚠️ Warning: The query ran successfully but returned no data.")
    sys.exit(0)

# --- Create DataFrames and calculate Closing Stock ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)", "Current_Stock"]].copy()
df_36m["Closing Stock (36m)"] = df_36m["Opening Stock (36m)"] + df_36m["Total Receipts (36m)"] + df_36m["Total Issues (36m)"]

df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)", "Current_Stock"]].copy()
df_9m["Closing Stock (9m)"] = df_9m["Opening Stock (9m)"] + df_9m["Total Receipts (9m)"] + df_9m["Total Issues (9m)"]

# --- Convert material numbers to short format (remove leading zeros) ---
df_36m["Material"] = df_36m["Material"].str.lstrip('0')
df_9m["Material"] = df_9m["Material"].str.lstrip('0')

# --- Remove the Current_Stock column before export ---
df_36m = df_36m.drop('Current_Stock', axis=1)
df_9m = df_9m.drop('Current_Stock', axis=1)

# --- Export DataFrames to CSV files ---
output_file_36m = 'final_raw_numbers_36_month.csv'
output_file_9m = 'final_raw_numbers_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n✅ Success! The MBEW-based solution has been exported to:")
print(f"   - {output_file_36m}")
print(f"   - {output_file_9m}")

# --- Show sample results for verification ---
print(f"\nSample of 36-month results:")
print(df_36m.head(10).to_string())

# --- Check specific materials for comparison ---
target_materials = ['7546978', '7546979', '7546980']
print(f"\n🎯 Results for target materials:")

for material in target_materials:
    print(f"\nMaterial {material}:")

    # 36-month data
    data_36 = df_36m[df_36m['Material'] == material]
    if not data_36.empty:
        print("36-month period:")
        for _, row in data_36.iterrows():
            print(f"  Plant {row['Plant']}: Opening={row['Opening Stock (36m)']:.1f}, Receipts={row['Total Receipts (36m)']:.1f}, Issues={row['Total Issues (36m)']:.1f}, Closing={row['Closing Stock (36m)']:.1f}")
    else:
        print("  No data found for 36-month period")

    # 9-month data
    data_9 = df_9m[df_9m['Material'] == material]
    if not data_9.empty:
        print("9-month period:")
        for _, row in data_9.iterrows():
            print(f"  Plant {row['Plant']}: Opening={row['Opening Stock (9m)']:.1f}, Receipts={row['Total Receipts (9m)']:.1f}, Issues={row['Total Issues (9m)']:.1f}, Closing={row['Closing Stock (9m)']:.1f}")
    else:
        print("  No data found for 9-month period")

print(f"\n📊 Summary: Retrieved {len(df_36m)} records for 36-month period and {len(df_9m)} records for 9-month period")