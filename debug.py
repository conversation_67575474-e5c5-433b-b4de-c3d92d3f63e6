import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks for Opening Stock debug.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)


# --- Define the single SKU we want to investigate ---
material_to_debug = '000000000007546978'
plant_to_debug = 'DE30'

# --- The start date for the 9-month period ---
period_start_date = '2024-12-01'


# --- SQL Query to pull the raw transaction log FOR OPENING STOCK ONLY ---
# This query pulls every historical movement before the period start date for one SKU.
query = f"""
SELECT
  mseg.matnr AS `Material`,
  mseg.werks AS `Plant`,
  mkpf.budat AS `Posting_Date`,
  mseg.menge AS `Quantity`,
  mseg.shkzg AS `Debit_Credit`,
  mseg.bwart AS `Movement_Type`,
  mseg.sobkz AS `Special_Stock_Indicator`
FROM
  brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
JOIN
  brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
ON
  mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
WHERE
  mseg.matnr = '{material_to_debug}' AND mseg.werks = '{plant_to_debug}'
  AND mkpf.budat < '{period_start_date}'
ORDER BY
  `Posting_Date`
"""

# --- Execute Query and Export Data ---
print(f"Executing query to fetch the opening stock transaction log for {material_to_debug} at {plant_to_debug}...")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished. Exporting raw data...")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

# --- Calculate the total that our script is getting ---
df['Signed_Quantity'] = df.apply(lambda row: row['Quantity'] if row['Debit_Credit'] == 'S' else -row['Quantity'], axis=1)
calculated_opening_stock = df['Signed_Quantity'].sum()

# --- Export DataFrame to a CSV file ---
output_file = 'debug_opening_stock_log.csv'
df.to_csv(output_file, index=False)

print(f"\n✅ Success! The detailed log for opening stock has been exported to:")
print(f"   - {output_file}")
print(f"\nOur script calculated an opening stock of: {calculated_opening_stock}")
print("Please analyze the CSV to see which movements should be included or excluded to match your SOT.")