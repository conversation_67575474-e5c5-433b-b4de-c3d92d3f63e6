import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Load SOT files to get the exact materials we need ---
print("📋 Loading SOT files to get required materials...")
sot_36m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv')

# Get unique materials from SOT files and format them correctly
sot_materials = set()
for _, row in sot_36m.iterrows():
    material_short = str(row['Material'])
    material_padded = f"000000000{material_short.zfill(9)}"  # Pad to 18 characters with leading zeros
    sot_materials.add(material_padded)

print(f"Found {len(sot_materials)} unique materials in SOT files")
print(f"Sample materials: {list(sot_materials)[:5]}")

# Create IN clause for SQL query
materials_list = "', '".join(sot_materials)

# --- Final comprehensive SQL Query ---
query = f"""
SELECT 
  mbew.matnr AS Material,
  mbew.bwkey AS Plant,
  COALESCE(mbew.lbkum, 0) AS Current_Stock,
  
  -- 36-month period movements (2022-09-30 to 2025-08-31)
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' 
                    THEN mseg.menge ELSE 0 END), 0) AS `Total Receipts (36m)`,
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' 
                    THEN -mseg.menge ELSE 0 END), 0) AS `Total Issues (36m)`,
  
  -- 9-month period movements (2024-12-01 to 2025-08-31)
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' 
                    THEN mseg.menge ELSE 0 END), 0) AS `Total Receipts (9m)`,
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' 
                    THEN -mseg.menge ELSE 0 END), 0) AS `Total Issues (9m)`

FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg 
  ON mbew.matnr = mseg.matnr AND mbew.bwkey = mseg.werks
LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
  ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
  AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'

WHERE mbew.matnr IN ('{materials_list}')
  AND mbew.bwkey LIKE 'DE%'

GROUP BY mbew.matnr, mbew.bwkey, mbew.lbkum
ORDER BY mbew.matnr, mbew.bwkey
"""

# --- Execute Query and Export Data ---
print("Executing comprehensive query on Databricks... This may take a moment.")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished. Processing and exporting data...")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

# --- Check if data was returned ---
if df.empty:
    print("\n⚠️ Warning: The query ran successfully but returned no data.")
    sys.exit(0)

print(f"Retrieved {len(df)} material-plant combinations")

# --- Calculate Opening Stock by working backwards from Current Stock ---
# Opening Stock = Current Stock - Net Movements in Period
df['Opening Stock (36m)'] = df['Current_Stock'] - df['Total Receipts (36m)'] - df['Total Issues (36m)']
df['Opening Stock (9m)'] = df['Current_Stock'] - df['Total Receipts (9m)'] - df['Total Issues (9m)']

# --- Calculate Closing Stock (should equal Current Stock) ---
df['Closing Stock (36m)'] = df['Opening Stock (36m)'] + df['Total Receipts (36m)'] + df['Total Issues (36m)']
df['Closing Stock (9m)'] = df['Opening Stock (9m)'] + df['Total Receipts (9m)'] + df['Total Issues (9m)']

# --- Create final DataFrames ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)", "Closing Stock (36m)"]].copy()
df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)", "Closing Stock (9m)"]].copy()

# --- Convert material numbers to short format (remove leading zeros) ---
df_36m["Material"] = df_36m["Material"].str.lstrip('0')
df_9m["Material"] = df_9m["Material"].str.lstrip('0')

# --- Export DataFrames to CSV files ---
output_file_36m = 'final_comprehensive_36_month.csv'
output_file_9m = 'final_comprehensive_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n✅ Success! The comprehensive solution has been exported to:")
print(f"   - {output_file_36m}")
print(f"   - {output_file_9m}")

# --- Show sample results for our target materials ---
target_materials = ['7546978', '7546979', '7546980']
print(f"\n🎯 Results for target materials:")

for material in target_materials:
    print(f"\nMaterial {material}:")
    
    # 36-month data
    data_36 = df_36m[df_36m['Material'] == material]
    if not data_36.empty:
        print("36-month period:")
        for _, row in data_36.iterrows():
            print(f"  Plant {row['Plant']}: Opening={row['Opening Stock (36m)']:.1f}, Receipts={row['Total Receipts (36m)']:.1f}, Issues={row['Total Issues (36m)']:.1f}, Closing={row['Closing Stock (36m)']:.1f}")
    
    # 9-month data
    data_9 = df_9m[df_9m['Material'] == material]
    if not data_9.empty:
        print("9-month period:")
        for _, row in data_9.iterrows():
            print(f"  Plant {row['Plant']}: Opening={row['Opening Stock (9m)']:.1f}, Receipts={row['Total Receipts (9m)']:.1f}, Issues={row['Total Issues (9m)']:.1f}, Closing={row['Closing Stock (9m)']:.1f}")

print(f"\n📊 Summary: Retrieved {len(df_36m)} records for 36-month period and {len(df_9m)} records for 9-month period")
