import pandas as pd
import numpy as np

# --- Configuration ---
# The file generated by our main calculation script
OUR_OUTPUT_FILE = 'impairment_summary_output.csv'
# The user's source-of-truth file for impairment values
SOURCE_TRUTH_FILE = 'DE Temp POCM Calculation P08 2025_TL.xlsx - Material Level Summary.csv'

def parse_source_truth_summary(filepath):
    """
    Parses the complex, report-style Material Level Summary CSV to extract
    clean, itemized impairment data. This is the "ground truth".
    """
    try:
        df = pd.read_csv(filepath, header=None, dtype=str)
        
        # Identify valid data rows (where the second column is a number)
        valid_rows = pd.to_numeric(df[1], errors='coerce').notna()
        df = df[valid_rows]
        
        # Name the columns based on their position in the report
        df = df[[0, 1, 2, 3]].rename(columns={
            0: 'plant_code', 1: 'material', 2: 'material_description', 3: 'reference_impairment'
        })
        
        # The report format leaves plant_code blank on subsequent rows; forward-fill to fix this.
        df['plant_code'].replace('', np.nan, inplace=True)
        df['plant_code'].ffill(inplace=True)
        
        # Clean up data types
        df['material'] = df['material'].astype(str)
        df['reference_impairment'] = pd.to_numeric(df['reference_impairment'], errors='coerce').fillna(0)

        # Remove any rows that might have been parsing artifacts
        df.dropna(subset=['plant_code', 'material'], inplace=True)
        
        return df[['plant_code', 'material', 'reference_impairment']]

    except FileNotFoundError:
        print(f"❌ FATAL: Source truth file not found: '{filepath}'")
        return None


def main():
    """
    Main function to run the reconciliation, identify discrepancies, and find the root cause.
    """
    print("--- Starting Reconciliation & Root Cause Analysis ---")
    
    # 1. Load our script's calculated results. It ALREADY contains the material_group.
    try:
        our_results = pd.read_csv(OUR_OUTPUT_FILE)
        our_results['material'] = our_results['material'].astype(str)
        print(f"✅ Loaded {len(our_results)} records from our calculation script.")
    except FileNotFoundError:
        print(f"❌ FATAL: Our output file not found: '{OUR_OUTPUT_FILE}'. Please run the main script first.")
        return

    # 2. Parse the complex source-of-truth file
    source_truth = parse_source_truth_summary(SOURCE_TRUTH_FILE)
    if source_truth is None:
        return
    print(f"✅ Parsed {len(source_truth)} records with impairment from the reference summary.")

    # 3. Merge the two datasets for a direct comparison
    comparison_df = pd.merge(
        our_results,
        source_truth,
        on=['plant_code', 'material'],
        how='left' # Left join to keep all our results and see which ones match
    )
    comparison_df['reference_impairment'] = comparison_df['reference_impairment'].fillna(0)

    # 4. Isolate the items we impaired but shouldn't have
    incorrectly_impaired_df = comparison_df[
        (comparison_df['impairment_value'] > 0.01) &  # We impaired it...
        (comparison_df['reference_impairment'] == 0)   # ...but the source says impairment is zero.
    ].copy() # Use .copy() to avoid SettingWithCopyWarning
    
    print(f"\n--- ANALYSIS COMPLETE ---")
    if incorrectly_impaired_df.empty:
        print("✅ SUCCESS: No incorrectly impaired items found. The logic appears correct.")
        return
        
    print(f"🔴 Found {len(incorrectly_impaired_df)} materials that our script impaired but the source file EXCLUDED.")

    # 5. Find the Root Cause by using the material_group ALREADY IN OUR DATA
    print("\n--- ROOT CAUSE IDENTIFIED ---")
    print("The discrepancy is because the following material groups are being impaired by our script,")
    print("but they are correctly excluded in the source-of-truth calculation.")
    
    # --- FIX ---
    # The 'material_group' column already exists in incorrectly_impaired_df.
    # No need to merge with the category file again.
    groups_to_exclude = sorted(list(incorrectly_impaired_df['material_group'].dropna().unique()))
    
    print("\n**Definitive List of Material Groups to Exclude:**")
    for group in groups_to_exclude:
        print(f"  - {group}")
        
    print("\n**Sample of Incorrectly Impaired Materials & Their Groups:**")
    print(incorrectly_impaired_df[['plant_code', 'material', 'material_group', 'impairment_value']].head(10).to_string(index=False))


if __name__ == "__main__":
    main()

