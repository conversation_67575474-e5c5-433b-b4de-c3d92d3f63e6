import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

print("🔬 INVESTIGATING SPECIFIC DISCREPANCIES")
print("=" * 60)

# Focus on the top discrepancies identified
problem_materials = [
    ('000000000007546989', 'DE02'),  # 560 unit difference
    ('000000000007546979', 'DE30'),  # 90 unit difference  
    ('000000000007546988', 'DE30'),  # 48 unit difference
]

for material, plant in problem_materials:
    material_short = material.lstrip('0')
    print(f"\n🎯 INVESTIGATING MATERIAL {material_short} AT PLANT {plant}")
    print("-" * 50)
    
    # Check for multiple MBEW records (different valuation types)
    mbew_query = f"""
    SELECT 
      matnr AS Material,
      bwkey AS Plant,
      bwtar AS Valuation_Type,
      lbkum AS Current_Stock,
      salk3 AS Current_Value
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
    WHERE matnr = '{material}' AND bwkey = '{plant}'
    ORDER BY bwtar
    """
    
    try:
        with conn.cursor() as cur:
            cur.execute(mbew_query)
            mbew_df = cur.fetchall_arrow().to_pandas()
        
        print("MBEW Records:")
        if not mbew_df.empty:
            total_stock = 0
            for _, row in mbew_df.iterrows():
                print(f"  ValType '{row['Valuation_Type']}': Stock={row['Current_Stock']:.3f}, Value={row['Current_Value']:.2f}")
                total_stock += row['Current_Stock']
            print(f"  TOTAL STOCK: {total_stock:.3f}")
        else:
            print("  No MBEW records found")
            
    except Exception as e:
        print(f"  Error checking MBEW: {e}")
    
    # Check for movements around boundary dates
    boundary_query = f"""
    SELECT 
      mkpf.budat AS Posting_Date,
      mseg.menge AS Quantity,
      mseg.shkzg AS Debit_Credit,
      mseg.bwart AS Movement_Type,
      mseg.sobkz AS Special_Stock
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
      ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
    WHERE mseg.matnr = '{material}' AND mseg.werks = '{plant}'
      AND mkpf.budat BETWEEN '2022-09-28' AND '2022-10-02'
    ORDER BY mkpf.budat
    """
    
    try:
        with conn.cursor() as cur:
            cur.execute(boundary_query)
            boundary_df = cur.fetchall_arrow().to_pandas()
        
        print("Movements around 36m boundary (2022-09-30):")
        if not boundary_df.empty:
            for _, row in boundary_df.iterrows():
                sign = "+" if row['Debit_Credit'] == 'S' else "-"
                special = f", Special: {row['Special_Stock']}" if pd.notna(row['Special_Stock']) else ""
                print(f"  {row['Posting_Date']}: {sign}{row['Quantity']:.3f} (Type: {row['Movement_Type']}{special})")
        else:
            print("  No movements found around boundary")
            
    except Exception as e:
        print(f"  Error checking boundary movements: {e}")

# Now let's check if there are any special stock types or movement types that might be filtered differently
print(f"\n🔍 CHECKING FOR SPECIAL STOCK INDICATORS")
print("=" * 60)

special_stock_query = """
SELECT 
  mseg.sobkz AS Special_Stock,
  COUNT(*) as Count,
  SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS Net_Quantity
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
  ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
WHERE mseg.matnr IN ('000000000007546989', '000000000007546979', '000000000007546988')
  AND mseg.werks IN ('DE02', 'DE30')
  AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
  AND mseg.sobkz IS NOT NULL AND mseg.sobkz != ''
GROUP BY mseg.sobkz
ORDER BY Net_Quantity DESC
"""

try:
    with conn.cursor() as cur:
        cur.execute(special_stock_query)
        special_df = cur.fetchall_arrow().to_pandas()
    
    if not special_df.empty:
        print("Special Stock Indicators found:")
        for _, row in special_df.iterrows():
            print(f"  Special Stock '{row['Special_Stock']}': {row['Count']} transactions, Net Qty: {row['Net_Quantity']:.3f}")
    else:
        print("No special stock indicators found")
        
except Exception as e:
    print(f"Error checking special stock: {e}")

# Check for specific movement types that might be treated differently
movement_type_query = """
SELECT 
  mseg.bwart AS Movement_Type,
  COUNT(*) as Count,
  SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS Net_Quantity
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
  ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
WHERE mseg.matnr IN ('000000000007546989', '000000000007546979', '000000000007546988')
  AND mseg.werks IN ('DE02', 'DE30')
  AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
GROUP BY mseg.bwart
HAVING ABS(SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END)) > 10
ORDER BY ABS(Net_Quantity) DESC
"""

try:
    with conn.cursor() as cur:
        cur.execute(movement_type_query)
        movement_df = cur.fetchall_arrow().to_pandas()
    
    print(f"\nMovement Types with significant quantities:")
    if not movement_df.empty:
        for _, row in movement_df.iterrows():
            print(f"  Movement Type '{row['Movement_Type']}': {row['Count']} transactions, Net Qty: {row['Net_Quantity']:.3f}")
    else:
        print("No significant movement types found")
        
except Exception as e:
    print(f"Error checking movement types: {e}")

conn.close()

print(f"\n" + "=" * 60)
print("🎯 INVESTIGATION COMPLETE")
print("   Check the findings above to identify potential causes:")
print("   1. Multiple valuation types in MBEW")
print("   2. Boundary date movements")
print("   3. Special stock indicators")
print("   4. Specific movement types")
print("=" * 60)
