import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Test query to check MBEW data for specific materials ---
test_query = """
SELECT 
  matnr AS Material,
  bwkey AS Plant,
  lbkum AS Current_Stock,
  salk3 AS Current_Value
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
WHERE matnr IN ('000000000007546978', '000000000007546979', '000000000007546980')
  AND bwkey LIKE 'DE%'
ORDER BY matnr, bwkey
"""

print("Testing MBEW table for specific materials...")
try:
    with conn.cursor() as cur:
        cur.execute(test_query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished.")
    
    if not df.empty:
        print(f"Found {len(df)} records in MBEW:")
        print(df.to_string())
        
        # Convert to short format for comparison
        df['Material_Short'] = df['Material'].str.lstrip('0')
        print(f"\nWith short material numbers:")
        print(df[['Material_Short', 'Plant', 'Current_Stock', 'Current_Value']].to_string())
    else:
        print("No data found in MBEW for these materials")
        
except Exception as e:
    print(f"An error occurred during query execution: {e}")
finally:
    conn.close()
