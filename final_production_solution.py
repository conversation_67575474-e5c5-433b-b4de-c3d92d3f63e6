import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

print("🚀 Starting Final Production Solution - MBEW-based Approach")
print("=" * 70)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Final Production SQL Query ---
# This uses the proven MBEW-based approach that showed perfect results in verification
query = """
WITH CurrentStock AS (
  SELECT 
    matnr AS Material,
    bwkey AS Plant,
    lbkum AS Current_Stock
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
  WHERE matnr LIKE '000000000007%' 
    AND bwkey LIKE 'DE%'
),

PeriodMovements AS (
  SELECT
    mseg.matnr AS Material,
    mseg.werks AS Plant,
    
    -- 36-month period movements (2022-09-30 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' 
             THEN mseg.menge ELSE 0 END) AS Receipts_36m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' 
             THEN -mseg.menge ELSE 0 END) AS Issues_36m,
    
    -- 9-month period movements (2024-12-01 to 2025-08-31)
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' 
             THEN mseg.menge ELSE 0 END) AS Receipts_9m,
    SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' 
             THEN -mseg.menge ELSE 0 END) AS Issues_9m
             
  FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
  JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
    ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
  WHERE mseg.matnr LIKE '000000000007%'
    AND mseg.werks LIKE 'DE%'
    AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
  GROUP BY mseg.matnr, mseg.werks
)

SELECT 
  cs.Material,
  cs.Plant,
  cs.Current_Stock,
  
  -- 36-month period calculations
  COALESCE(pm.Receipts_36m, 0) AS `Total Receipts (36m)`,
  COALESCE(pm.Issues_36m, 0) AS `Total Issues (36m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_36m, 0) - COALESCE(pm.Issues_36m, 0)) AS `Opening Stock (36m)`,
  
  -- 9-month period calculations  
  COALESCE(pm.Receipts_9m, 0) AS `Total Receipts (9m)`,
  COALESCE(pm.Issues_9m, 0) AS `Total Issues (9m)`,
  -- Opening Stock = Current Stock - Net Movements in Period
  (cs.Current_Stock - COALESCE(pm.Receipts_9m, 0) - COALESCE(pm.Issues_9m, 0)) AS `Opening Stock (9m)`

FROM CurrentStock cs
LEFT JOIN PeriodMovements pm 
  ON cs.Material = pm.Material AND cs.Plant = pm.Plant
ORDER BY cs.Material, cs.Plant
"""

# --- Execute Query ---
print("📊 Executing production query on Databricks...")
print("   This query uses the MBEW table for current stock and calculates backwards")
print("   to derive opening stock - the approach that showed perfect results in verification.")

try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("✅ Query completed successfully.")
except Exception as e:
    print(f"❌ An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

if df.empty:
    print("⚠️ Warning: The query returned no data.")
    sys.exit(0)

print(f"📈 Retrieved {len(df)} material-plant combinations")

# --- Calculate Closing Stock ---
df['Closing Stock (36m)'] = df['Opening Stock (36m)'] + df['Total Receipts (36m)'] + df['Total Issues (36m)']
df['Closing Stock (9m)'] = df['Opening Stock (9m)'] + df['Total Receipts (9m)'] + df['Total Issues (9m)']

# --- Create final DataFrames ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)", "Closing Stock (36m)"]].copy()
df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)", "Closing Stock (9m)"]].copy()

# --- Convert material numbers to short format (remove leading zeros) ---
df_36m["Material"] = df_36m["Material"].str.lstrip('0')
df_9m["Material"] = df_9m["Material"].str.lstrip('0')

# --- Export DataFrames to CSV files ---
output_file_36m = 'final_raw_numbers_36_month.csv'
output_file_9m = 'final_raw_numbers_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n🎉 SUCCESS! The final production solution has been exported to:")
print(f"   📄 {output_file_36m} ({len(df_36m)} records)")
print(f"   📄 {output_file_9m} ({len(df_9m)} records)")

# --- Verification: Show results for our test materials ---
target_materials = ['7546978', '7546979', '7546980']
print(f"\n🔍 Verification - Results for test materials:")
print("=" * 50)

for material in target_materials:
    print(f"\nMaterial {material}:")
    
    # 36-month data
    data_36 = df_36m[df_36m['Material'] == material]
    if not data_36.empty:
        print("  36-month period:")
        for _, row in data_36.iterrows():
            print(f"    Plant {row['Plant']}: Opening={row['Opening Stock (36m)']:.1f}, Receipts={row['Total Receipts (36m)']:.1f}, Issues={row['Total Issues (36m)']:.1f}, Closing={row['Closing Stock (36m)']:.1f}")
    
    # 9-month data
    data_9 = df_9m[df_9m['Material'] == material]
    if not data_9.empty:
        print("  9-month period:")
        for _, row in data_9.iterrows():
            print(f"    Plant {row['Plant']}: Opening={row['Opening Stock (9m)']:.1f}, Receipts={row['Total Receipts (9m)']:.1f}, Issues={row['Total Issues (9m)']:.1f}, Closing={row['Closing Stock (9m)']:.1f}")

print("\n" + "=" * 70)
print("✅ FINAL SOLUTION COMPLETE!")
print("   The files are ready for impairment calculation.")
print("   This solution uses the MBEW table approach that showed perfect")
print("   alignment with the Source of Truth files in our verification.")
print("=" * 70)
