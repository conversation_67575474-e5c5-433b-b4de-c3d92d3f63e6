import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

print("🔍 RECONCILIATION ANALYSIS - Finding Root Cause of Discrepancies")
print("=" * 80)

# Load SOT files for comparison
sot_36m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 36 MB5B RD.csv')
sot_9m = pd.read_csv('DE Temp POCM Calculation P08 2025_TL.xlsx - 09 MB5B RD.csv')

# Load our results
our_36m = pd.read_csv('final_raw_numbers_36_month.csv')
our_9m = pd.read_csv('final_raw_numbers_9_month.csv')

# Focus on materials with discrepancies
problem_materials = [
    ('7546979', 'DE30'),  # 90 unit difference
    ('7546980', 'DE02'),  # 6-108 unit difference
]

for material, plant in problem_materials:
    print(f"\n🎯 ANALYZING MATERIAL {material} AT PLANT {plant}")
    print("-" * 60)
    
    # Get SOT data
    sot_36_row = sot_36m[(sot_36m['Material'] == int(material)) & (sot_36m['Plnt'] == plant)]
    sot_9_row = sot_9m[(sot_9m['Material'] == int(material)) & (sot_9m['Plnt'] == plant)]
    
    # Get our data
    our_36_row = our_36m[(our_36m['Material'] == material) & (our_36m['Plant'] == plant)]
    our_9_row = our_9m[(our_9m['Material'] == material) & (our_9m['Plant'] == plant)]
    
    if not sot_36_row.empty and not our_36_row.empty:
        sot_36_data = sot_36_row.iloc[0]
        our_36_data = our_36_row.iloc[0]
        
        print("36-Month Period Comparison:")
        print(f"  Opening Stock: SOT={sot_36_data['Opening St']}, Ours={our_36_data['Opening Stock (36m)']:.1f}, Diff={our_36_data['Opening Stock (36m)'] - sot_36_data['Opening St']:.1f}")
        print(f"  Total Receipts: SOT={sot_36_data['Total Rece']}, Ours={our_36_data['Total Receipts (36m)']:.1f}, Diff={our_36_data['Total Receipts (36m)'] - sot_36_data['Total Rece']:.1f}")
        print(f"  Total Issues: SOT={sot_36_data['Total Issu']}, Ours={our_36_data['Total Issues (36m)']:.1f}, Diff={our_36_data['Total Issues (36m)'] - sot_36_data['Total Issu']:.1f}")
        print(f"  Closing Stock: SOT={sot_36_data['Closing St']}, Ours={our_36_data['Closing Stock (36m)']:.1f}, Diff={our_36_data['Closing Stock (36m)'] - sot_36_data['Closing St']:.1f}")
    
    if not sot_9_row.empty and not our_9_row.empty:
        sot_9_data = sot_9_row.iloc[0]
        our_9_data = our_9_row.iloc[0]
        
        print("9-Month Period Comparison:")
        print(f"  Opening Stock: SOT={sot_9_data['Opening St']}, Ours={our_9_data['Opening Stock (9m)']:.1f}, Diff={our_9_data['Opening Stock (9m)'] - sot_9_data['Opening St']:.1f}")
        print(f"  Total Receipts: SOT={sot_9_data['Total Rece']}, Ours={our_9_data['Total Receipts (9m)']:.1f}, Diff={our_9_data['Total Receipts (9m)'] - sot_9_data['Total Rece']:.1f}")
        print(f"  Total Issues: SOT={sot_9_data['Total Issu']}, Ours={our_9_data['Total Issues (9m)']:.1f}, Diff={our_9_data['Total Issues (9m)'] - sot_9_data['Total Issu']:.1f}")
        print(f"  Closing Stock: SOT={sot_9_data['Closing St']}, Ours={our_9_data['Closing Stock (9m)']:.1f}, Diff={our_9_data['Closing Stock (9m)'] - sot_9_data['Closing St']:.1f}")

# Now let's investigate potential causes
print(f"\n🔬 DETAILED INVESTIGATION - Potential Causes")
print("=" * 80)

# Check if there are multiple MBEW records for the same material-plant
mbew_check_query = """
SELECT 
  matnr AS Material,
  bwkey AS Plant,
  bwtar AS Valuation_Type,
  lbkum AS Current_Stock,
  salk3 AS Current_Value
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
WHERE matnr IN ('000000000007546979', '000000000007546980')
  AND bwkey IN ('DE30', 'DE02')
ORDER BY matnr, bwkey, bwtar
"""

print("1. Checking for multiple MBEW records (different valuation types)...")
try:
    with conn.cursor() as cur:
        cur.execute(mbew_check_query)
        mbew_df = cur.fetchall_arrow().to_pandas()
    
    if not mbew_df.empty:
        print("MBEW Records Found:")
        for _, row in mbew_df.iterrows():
            material_short = row['Material'].lstrip('0')
            print(f"  Material {material_short}, Plant {row['Plant']}, ValType '{row['Valuation_Type']}': Stock={row['Current_Stock']:.3f}, Value={row['Current_Value']:.2f}")
    else:
        print("No MBEW records found")
        
except Exception as e:
    print(f"Error checking MBEW: {e}")

# Check for movements on the boundary dates
boundary_check_query = """
SELECT 
  mseg.matnr AS Material,
  mseg.werks AS Plant,
  mkpf.budat AS Posting_Date,
  mseg.menge AS Quantity,
  mseg.shkzg AS Debit_Credit,
  mseg.bwart AS Movement_Type,
  mseg.sobkz AS Special_Stock
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
  ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
WHERE mseg.matnr IN ('000000000007546979', '000000000007546980')
  AND mseg.werks IN ('DE30', 'DE02')
  AND mkpf.budat IN ('2022-09-29', '2022-09-30', '2024-11-30', '2024-12-01')
ORDER BY mseg.matnr, mseg.werks, mkpf.budat
"""

print("\n2. Checking for movements on boundary dates (period start/end)...")
try:
    with conn.cursor() as cur:
        cur.execute(boundary_check_query)
        boundary_df = cur.fetchall_arrow().to_pandas()
    
    if not boundary_df.empty:
        print("Boundary Date Movements:")
        for _, row in boundary_df.iterrows():
            material_short = row['Material'].lstrip('0')
            sign = "+" if row['Debit_Credit'] == 'S' else "-"
            print(f"  Material {material_short}, Plant {row['Plant']}, Date {row['Posting_Date']}: {sign}{row['Quantity']:.3f} (Type: {row['Movement_Type']})")
    else:
        print("No movements found on boundary dates")
        
except Exception as e:
    print(f"Error checking boundary dates: {e}")

# Check for special stock indicators or movement types that might be filtered differently
special_movements_query = """
SELECT 
  mseg.matnr AS Material,
  mseg.werks AS Plant,
  mseg.bwart AS Movement_Type,
  mseg.sobkz AS Special_Stock,
  COUNT(*) as Count,
  SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS Net_Quantity
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
  ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
WHERE mseg.matnr IN ('000000000007546979', '000000000007546980')
  AND mseg.werks IN ('DE30', 'DE02')
  AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'
  AND (mseg.sobkz IS NOT NULL AND mseg.sobkz != '' OR mseg.bwart IN ('101', '102', '201', '202', '261', '262', '301', '302'))
GROUP BY mseg.matnr, mseg.werks, mseg.bwart, mseg.sobkz
ORDER BY mseg.matnr, mseg.werks, Net_Quantity DESC
"""

print("\n3. Checking for special stock indicators or specific movement types...")
try:
    with conn.cursor() as cur:
        cur.execute(special_movements_query)
        special_df = cur.fetchall_arrow().to_pandas()
    
    if not special_df.empty:
        print("Special Movements Found:")
        for _, row in special_df.iterrows():
            material_short = row['Material'].lstrip('0')
            special_stock = row['Special_Stock'] if pd.notna(row['Special_Stock']) else 'None'
            print(f"  Material {material_short}, Plant {row['Plant']}, MovType {row['Movement_Type']}, SpecStock '{special_stock}': {row['Count']} transactions, Net={row['Net_Quantity']:.3f}")
    else:
        print("No special movements found")
        
except Exception as e:
    print(f"Error checking special movements: {e}")

conn.close()

print(f"\n" + "=" * 80)
print("🎯 RECONCILIATION ANALYSIS COMPLETE")
print("   Review the findings above to identify the source of discrepancies.")
print("   Common causes:")
print("   1. Multiple valuation types in MBEW table")
print("   2. Movements on boundary dates (inclusive/exclusive differences)")
print("   3. Special stock indicators or movement types")
print("   4. Rounding differences in calculations")
print("=" * 80)
