import os
import sys
import pandas as pd
from databricks import sql
from dotenv import load_dotenv

# --- Load Environment Variables from .env file ---
load_dotenv()

# --- Databricks Connection Details ---
host = os.getenv("DATABRICKS_HOST")
http_path = os.getenv("DATABRICKS_HTTP_PATH")
token = os.getenv("DATABRICKS_TOKEN")

# --- Verify that environment variables are set ---
if not all([host, http_path, token]):
    print("Error: Databricks environment variables not found.")
    sys.exit(1)

# --- Connect to Databricks ---
try:
    conn = sql.connect(
        server_hostname=host,
        http_path=http_path,
        access_token=token
    )
    print("✅ Successfully connected to Databricks.")
except Exception as e:
    print(f"Failed to connect to Databricks: {e}")
    sys.exit(1)

# --- Final SQL Query using MBEW for current stock and MSEG for movements ---
# This approach:
# 1. Gets current stock from MBEW table (lbkum field)
# 2. Calculates period movements from MSEG/MKPF
# 3. Derives opening stock by working backwards from current stock
query = """
SELECT 
  mbew.matnr AS Material,
  mbew.bwkey AS Plant,
  mbew.lbkum AS Current_Stock,
  
  -- 36-month period movements (2022-09-30 to 2025-08-31)
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'S' 
                    THEN mseg.menge ELSE 0 END), 0) AS `Total Receipts (36m)`,
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31' AND mseg.shkzg = 'H' 
                    THEN -mseg.menge ELSE 0 END), 0) AS `Total Issues (36m)`,
  
  -- 9-month period movements (2024-12-01 to 2025-08-31)
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'S' 
                    THEN mseg.menge ELSE 0 END), 0) AS `Total Receipts (9m)`,
  COALESCE(SUM(CASE WHEN mkpf.budat BETWEEN '2024-12-01' AND '2025-08-31' AND mseg.shkzg = 'H' 
                    THEN -mseg.menge ELSE 0 END), 0) AS `Total Issues (9m)`

FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew
LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg 
  ON mbew.matnr = mseg.matnr AND mbew.bwkey = mseg.werks
LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mkpf
  ON mseg.mblnr = mkpf.mblnr AND mseg.mjahr = mkpf.mjahr
  AND mkpf.budat BETWEEN '2022-09-30' AND '2025-08-31'

WHERE mbew.matnr LIKE '000000000007%' 
  AND mbew.bwkey LIKE 'DE%'
  AND mbew.lbkum != 0  -- Only include materials with current stock

GROUP BY mbew.matnr, mbew.bwkey, mbew.lbkum
ORDER BY mbew.matnr, mbew.bwkey
"""

# --- Execute Query and Export Data ---
print("Executing MBEW-based query on Databricks... This may take a moment.")
try:
    with conn.cursor() as cur:
        cur.execute(query)
        df = cur.fetchall_arrow().to_pandas()
    print("Query finished. Processing and exporting data...")
except Exception as e:
    print(f"An error occurred during query execution: {e}")
    sys.exit(1)
finally:
    conn.close()

# --- Check if data was returned ---
if df.empty:
    print("\n⚠️ Warning: The query ran successfully but returned no data.")
    sys.exit(0)

print(f"Retrieved {len(df)} material-plant combinations")

# --- Calculate Opening Stock by working backwards from Current Stock ---
# Opening Stock = Current Stock - Net Movements in Period
df['Opening Stock (36m)'] = df['Current_Stock'] - df['Total Receipts (36m)'] - df['Total Issues (36m)']
df['Opening Stock (9m)'] = df['Current_Stock'] - df['Total Receipts (9m)'] - df['Total Issues (9m)']

# --- Calculate Closing Stock (should equal Current Stock) ---
df['Closing Stock (36m)'] = df['Opening Stock (36m)'] + df['Total Receipts (36m)'] + df['Total Issues (36m)']
df['Closing Stock (9m)'] = df['Opening Stock (9m)'] + df['Total Receipts (9m)'] + df['Total Issues (9m)']

# --- Create final DataFrames ---
df_36m = df[["Material", "Plant", "Opening Stock (36m)", "Total Receipts (36m)", "Total Issues (36m)", "Closing Stock (36m)"]].copy()
df_9m = df[["Material", "Plant", "Opening Stock (9m)", "Total Receipts (9m)", "Total Issues (9m)", "Closing Stock (9m)"]].copy()

# --- Convert material numbers to short format (remove leading zeros) ---
df_36m["Material"] = df_36m["Material"].str.lstrip('0')
df_9m["Material"] = df_9m["Material"].str.lstrip('0')

# --- Export DataFrames to CSV files ---
output_file_36m = 'mbew_solution_36_month.csv'
output_file_9m = 'mbew_solution_9_month.csv'

df_36m.to_csv(output_file_36m, index=False)
df_9m.to_csv(output_file_9m, index=False)

print(f"\n✅ Success! The MBEW-based solution has been exported to:")
print(f"   - {output_file_36m}")
print(f"   - {output_file_9m}")

# --- Show sample of results for verification ---
print(f"\nSample of 36-month results:")
print(df_36m.head(10).to_string())
print(f"\nSample of 9-month results:")
print(df_9m.head(10).to_string())

# --- Verification: Check if closing stock matches current stock ---
print(f"\n🔍 Verification - Checking if calculated closing stock matches current stock:")
df_check = df[['Material', 'Plant', 'Current_Stock', 'Closing Stock (36m)', 'Closing Stock (9m)']]
df_check['Material_Short'] = df_check['Material'].str.lstrip('0')
print(df_check[['Material_Short', 'Plant', 'Current_Stock', 'Closing Stock (36m)', 'Closing Stock (9m)']].head(10).to_string())
